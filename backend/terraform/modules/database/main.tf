# DynamoDB Tables Module for GameFlex Backend

# Local values for table configuration
locals {
  table_config = {
    billing_mode   = var.billing_mode
    deletion_protection = var.is_production_or_staging
    point_in_time_recovery = var.is_production_or_staging
  }
  
  name_prefix = "${var.project_name}-${var.environment}"
}

# Posts Table
resource "aws_dynamodb_table" "posts" {
  name           = "${local.name_prefix}-Posts"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "id"
  
  attribute {
    name = "id"
    type = "S"
  }
  
  # Enable deletion protection for production/staging
  deletion_protection_enabled = local.table_config.deletion_protection
  
  # Point-in-time recovery
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Posts"
  })
}

# Media Table
resource "aws_dynamodb_table" "media" {
  name           = "${local.name_prefix}-Media"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "id"
  
  attribute {
    name = "id"
    type = "S"
  }
  
  deletion_protection_enabled = local.table_config.deletion_protection
  
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Media"
  })
}

# User Profiles Table
resource "aws_dynamodb_table" "user_profiles" {
  name           = "${local.name_prefix}-UserProfiles"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "userId"
  
  attribute {
    name = "userId"
    type = "S"
  }
  
  deletion_protection_enabled = local.table_config.deletion_protection
  
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-UserProfiles"
  })
}

# Users Table
resource "aws_dynamodb_table" "users" {
  name           = "${local.name_prefix}-Users"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "id"
  
  attribute {
    name = "id"
    type = "S"
  }
  
  attribute {
    name = "username"
    type = "S"
  }

  attribute {
    name = "email"
    type = "S"
  }

  attribute {
    name = "cognitoUserId"
    type = "S"
  }

  attribute {
    name = "xboxUserId"
    type = "S"
  }

  # GSI for username lookup
  global_secondary_index {
    name               = "UsernameIndex"
    hash_key           = "username"
    projection_type    = "ALL"
  }

  # GSI for email lookup
  global_secondary_index {
    name               = "EmailIndex"
    hash_key           = "email"
    projection_type    = "ALL"
  }

  # GSI for Cognito user ID lookup
  global_secondary_index {
    name               = "CognitoUserIdIndex"
    hash_key           = "cognitoUserId"
    projection_type    = "ALL"
  }

  # GSI for Xbox user ID lookup
  global_secondary_index {
    name               = "XboxUserIdIndex"
    hash_key           = "xboxUserId"
    projection_type    = "ALL"
  }
  
  deletion_protection_enabled = local.table_config.deletion_protection
  
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Users"
  })
}

# Likes Table
resource "aws_dynamodb_table" "likes" {
  name           = "${local.name_prefix}-Likes"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "postId"
  range_key      = "userId"
  
  attribute {
    name = "postId"
    type = "S"
  }
  
  attribute {
    name = "userId"
    type = "S"
  }
  
  # GSI for user likes lookup
  global_secondary_index {
    name               = "userIdIndex"
    hash_key           = "userId"
    projection_type    = "ALL"
  }
  
  deletion_protection_enabled = local.table_config.deletion_protection
  
  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }
  
  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Likes"
  })
}

# Follows Table
resource "aws_dynamodb_table" "follows" {
  name           = "${local.name_prefix}-Follows"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "followerId"
  range_key      = "followingId"

  attribute {
    name = "followerId"
    type = "S"
  }

  attribute {
    name = "followingId"
    type = "S"
  }

  # GSI for looking up who a user is following
  global_secondary_index {
    name               = "userIdIndex"
    hash_key           = "followerId"
    projection_type    = "ALL"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Follows"
  })
}

# Channels Table
resource "aws_dynamodb_table" "channels" {
  name           = "${local.name_prefix}-Channels"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Channels"
  })
}

# Channel Members Table
resource "aws_dynamodb_table" "channel_members" {
  name           = "${local.name_prefix}-ChannelMembers"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "channelId"
  range_key      = "userId"

  attribute {
    name = "channelId"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  # GSI for looking up channels by user
  global_secondary_index {
    name               = "userIdIndex"
    hash_key           = "userId"
    projection_type    = "ALL"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-ChannelMembers"
  })
}

# Reflexes Table
resource "aws_dynamodb_table" "reflexes" {
  name           = "${local.name_prefix}-Reflexes"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "postId"
  range_key      = "id"

  attribute {
    name = "postId"
    type = "S"
  }

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  # GSI for user reflexes lookup
  global_secondary_index {
    name               = "userIdIndex"
    hash_key           = "userId"
    projection_type    = "ALL"
  }

  # GSI for reflex ID lookup
  global_secondary_index {
    name               = "idIndex"
    hash_key           = "id"
    projection_type    = "ALL"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Reflexes"
  })
}

# Reflex Likes Table
resource "aws_dynamodb_table" "reflex_likes" {
  name           = "${local.name_prefix}-ReflexLikes"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "reflexId"
  range_key      = "userId"

  attribute {
    name = "reflexId"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-ReflexLikes"
  })
}

# Reflex Reactions Table
resource "aws_dynamodb_table" "reflex_reactions" {
  name           = "${local.name_prefix}-ReflexReactions"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "reflexId"
  range_key      = "reactionKey"

  attribute {
    name = "reflexId"
    type = "S"
  }

  attribute {
    name = "reactionKey"
    type = "S"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-ReflexReactions"
  })
}

# Comments Table
resource "aws_dynamodb_table" "comments" {
  name           = "${local.name_prefix}-Comments"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "postId"
  range_key      = "id"

  attribute {
    name = "postId"
    type = "S"
  }

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  # GSI for user comments lookup
  global_secondary_index {
    name               = "userIdIndex"
    hash_key           = "userId"
    projection_type    = "ALL"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-Comments"
  })
}

# Xbox Accounts Table
resource "aws_dynamodb_table" "xbox_accounts" {
  name           = "${local.name_prefix}-XboxAccounts"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "xboxUserId"
    type = "S"
  }

  # GSI for user ID lookup
  global_secondary_index {
    name               = "UserIdIndex"
    hash_key           = "userId"
    projection_type    = "ALL"
  }

  # GSI for Xbox user ID lookup
  global_secondary_index {
    name               = "XboxUserIdIndex"
    hash_key           = "xboxUserId"
    projection_type    = "ALL"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-XboxAccounts"
  })
}

# Post Views Table
resource "aws_dynamodb_table" "post_views" {
  name           = "${local.name_prefix}-PostViews"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "postId"
  range_key      = "viewId"

  attribute {
    name = "postId"
    type = "S"
  }

  attribute {
    name = "viewId"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  attribute {
    name = "timestamp"
    type = "S"
  }

  # GSI for user views lookup
  global_secondary_index {
    name               = "userIdTimestampIndex"
    hash_key           = "userId"
    range_key          = "timestamp"
    projection_type    = "ALL"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-PostViews"
  })
}

# Post Reactions Table
resource "aws_dynamodb_table" "post_reactions" {
  name           = "${local.name_prefix}-PostReactions"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "postId"
  range_key      = "reactionKey"

  attribute {
    name = "postId"
    type = "S"
  }

  attribute {
    name = "reactionKey"
    type = "S"
  }

  attribute {
    name = "userId"
    type = "S"
  }

  # GSI for user reactions lookup
  global_secondary_index {
    name               = "userIdIndex"
    hash_key           = "userId"
    projection_type    = "ALL"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-PostReactions"
  })
}

# Post Engagement Metrics Table
resource "aws_dynamodb_table" "post_engagement_metrics" {
  name           = "${local.name_prefix}-PostEngagementMetrics"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "postId"

  attribute {
    name = "postId"
    type = "S"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-PostEngagementMetrics"
  })
}

# User Preferences Table
resource "aws_dynamodb_table" "user_preferences" {
  name           = "${local.name_prefix}-UserPreferences"
  billing_mode   = local.table_config.billing_mode
  hash_key       = "userId"

  attribute {
    name = "userId"
    type = "S"
  }

  deletion_protection_enabled = local.table_config.deletion_protection

  point_in_time_recovery {
    enabled = local.table_config.point_in_time_recovery
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-UserPreferences"
  })
}
