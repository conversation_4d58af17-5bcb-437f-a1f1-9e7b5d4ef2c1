# Landing Page Module for GameFlex Backend - Static Website Hosting

locals {
  name_prefix = "${var.project_name}-${var.environment}"
  bucket_name = "${local.name_prefix}-landing-page"
}

# S3 Bucket for Static Website Hosting
resource "aws_s3_bucket" "landing_page" {
  bucket        = local.bucket_name
  force_destroy = !var.is_production_or_staging
  
  tags = merge(var.tags, {
    Name = local.bucket_name
    Type = "Static Website"
  })
}

# S3 Bucket Website Configuration
resource "aws_s3_bucket_website_configuration" "landing_page" {
  bucket = aws_s3_bucket.landing_page.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "404.html"
  }
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "landing_page" {
  bucket = aws_s3_bucket.landing_page.id
  versioning_configuration {
    status = var.is_production_or_staging ? "Enabled" : "Suspended"
  }
}

# S3 Bucket Server-Side Encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "landing_page" {
  bucket = aws_s3_bucket.landing_page.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

# S3 Bucket Public Access Block
resource "aws_s3_bucket_public_access_block" "landing_page" {
  bucket = aws_s3_bucket.landing_page.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# CloudFront Origin Access Control
resource "aws_cloudfront_origin_access_control" "landing_page" {
  name                              = "${local.name_prefix}-landing-page-oac"
  description                       = "OAC for ${local.name_prefix} landing page bucket"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

# CloudFront Distribution
resource "aws_cloudfront_distribution" "landing_page" {
  origin {
    domain_name              = aws_s3_bucket.landing_page.bucket_regional_domain_name
    origin_access_control_id = aws_cloudfront_origin_access_control.landing_page.id
    origin_id                = "S3-${aws_s3_bucket.landing_page.bucket}"
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = "GameFlex Landing Page CDN - ${var.environment}"
  default_root_object = "index.html"

  # Custom domain configuration
  aliases = var.certificate_arn != "" && var.environment == "production" ? ["gameflex.io"] : []

  default_cache_behavior {
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${aws_s3_bucket.landing_page.bucket}"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 86400
    max_ttl                = 31536000

    # Enable compression
    compress = true
  }

  # Cache behavior for static assets
  ordered_cache_behavior {
    path_pattern     = "/assets/*"
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${aws_s3_bucket.landing_page.bucket}"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    min_ttl                = 0
    default_ttl            = 31536000
    max_ttl                = 31536000
    compress               = true
    viewer_protocol_policy = "redirect-to-https"
  }

  # Custom error responses
  custom_error_response {
    error_code         = 404
    response_code      = 404
    response_page_path = "/404.html"
  }

  custom_error_response {
    error_code         = 403
    response_code      = 404
    response_page_path = "/404.html"
  }

  price_class = var.price_class

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  # SSL Certificate configuration
  dynamic "viewer_certificate" {
    for_each = var.certificate_arn != "" ? [1] : []
    content {
      acm_certificate_arn      = var.certificate_arn
      ssl_support_method       = "sni-only"
      minimum_protocol_version = "TLSv1.2_2021"
    }
  }

  dynamic "viewer_certificate" {
    for_each = var.certificate_arn == "" ? [1] : []
    content {
      cloudfront_default_certificate = true
    }
  }

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-landing-page-distribution"
    Type = "Static Website CDN"
  })
}

# S3 Bucket Policy for CloudFront
resource "aws_s3_bucket_policy" "landing_page" {
  bucket = aws_s3_bucket.landing_page.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "AllowCloudFrontServicePrincipal"
        Effect    = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${aws_s3_bucket.landing_page.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = aws_cloudfront_distribution.landing_page.arn
          }
        }
      }
    ]
  })
}

# Route53 Records (only for production)
data "aws_route53_zone" "main" {
  count = var.environment == "production" && var.certificate_arn != "" ? 1 : 0
  name  = "gameflex.io"
}

# A record for root domain (production only)
resource "aws_route53_record" "landing_page_a" {
  count   = var.environment == "production" && var.certificate_arn != "" ? 1 : 0
  zone_id = data.aws_route53_zone.main[0].zone_id
  name    = "gameflex.io"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.landing_page.domain_name
    zone_id                = aws_cloudfront_distribution.landing_page.hosted_zone_id
    evaluate_target_health = false
  }
}

# AAAA record for IPv6 (production only)
resource "aws_route53_record" "landing_page_aaaa" {
  count   = var.environment == "production" && var.certificate_arn != "" ? 1 : 0
  zone_id = data.aws_route53_zone.main[0].zone_id
  name    = "gameflex.io"
  type    = "AAAA"

  alias {
    name                   = aws_cloudfront_distribution.landing_page.domain_name
    zone_id                = aws_cloudfront_distribution.landing_page.hosted_zone_id
    evaluate_target_health = false
  }
}
