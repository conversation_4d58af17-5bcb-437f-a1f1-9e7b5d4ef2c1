# Outputs for Landing Page Module

# S3 Bucket outputs
output "bucket" {
  description = "S3 landing page bucket object"
  value       = aws_s3_bucket.landing_page
}

output "bucket_name" {
  description = "Name of the landing page S3 bucket"
  value       = aws_s3_bucket.landing_page.bucket
}

output "bucket_arn" {
  description = "ARN of the landing page S3 bucket"
  value       = aws_s3_bucket.landing_page.arn
}

output "bucket_domain_name" {
  description = "Domain name of the landing page S3 bucket"
  value       = aws_s3_bucket.landing_page.bucket_domain_name
}

output "bucket_regional_domain_name" {
  description = "Regional domain name of the landing page S3 bucket"
  value       = aws_s3_bucket.landing_page.bucket_regional_domain_name
}

output "bucket_website_endpoint" {
  description = "Website endpoint of the landing page S3 bucket"
  value       = aws_s3_bucket_website_configuration.landing_page.website_endpoint
}

# CloudFront Distribution outputs
output "distribution" {
  description = "CloudFront distribution object"
  value       = aws_cloudfront_distribution.landing_page
}

output "distribution_id" {
  description = "ID of the CloudFront distribution"
  value       = aws_cloudfront_distribution.landing_page.id
}

output "distribution_arn" {
  description = "ARN of the CloudFront distribution"
  value       = aws_cloudfront_distribution.landing_page.arn
}

output "distribution_domain_name" {
  description = "Domain name of the CloudFront distribution"
  value       = aws_cloudfront_distribution.landing_page.domain_name
}

output "distribution_url" {
  description = "URL of the CloudFront distribution"
  value       = "https://${aws_cloudfront_distribution.landing_page.domain_name}"
}

output "distribution_hosted_zone_id" {
  description = "Hosted zone ID of the CloudFront distribution"
  value       = aws_cloudfront_distribution.landing_page.hosted_zone_id
}

# Route53 outputs (if created)
output "route53_record_names" {
  description = "Names of Route53 records created"
  value = var.environment == "production" && var.certificate_arn != "" ? [
    aws_route53_record.landing_page_a[0].name,
    aws_route53_record.landing_page_aaaa[0].name
  ] : []
}
