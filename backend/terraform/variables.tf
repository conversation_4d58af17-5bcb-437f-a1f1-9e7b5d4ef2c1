# Variables for GameFlex Backend Infrastructure

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "gameflex"
}

variable "environment" {
  description = "Environment name (development, staging, production)"
  type        = string
  validation {
    condition     = contains(["development", "staging", "production"], var.environment)
    error_message = "Environment must be one of: development, staging, production."
  }
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
}

variable "aws_account_id" {
  description = "AWS account ID"
  type        = string
}

# Domain Configuration
variable "domain_name" {
  description = "Custom domain name for API Gateway"
  type        = string
  default     = ""
}

variable "media_domain_name" {
  description = "Custom domain name for media CDN"
  type        = string
  default     = ""
}

# Certificate ARNs
variable "certificate_arn" {
  description = "ACM certificate ARN for API Gateway domain"
  type        = string
  default     = ""
}

variable "media_certificate_arn" {
  description = "ACM certificate ARN for media domain"
  type        = string
  default     = ""
}

variable "landing_page_certificate_arn" {
  description = "ACM certificate ARN for landing page domain"
  type        = string
  default     = ""
}

# Secrets Manager Secret Names
variable "app_config_secret_name" {
  description = "Name of the app configuration secret in Secrets Manager"
  type        = string
}

variable "apple_config_secret_name" {
  description = "Name of the Apple configuration secret in Secrets Manager"
  type        = string
}

variable "xbox_config_secret_name" {
  description = "Name of the Xbox configuration secret in Secrets Manager"
  type        = string
}

# Lambda Configuration
variable "lambda_runtime" {
  description = "Runtime for Lambda functions"
  type        = string
  default     = "nodejs22.x"
}

variable "lambda_timeout" {
  description = "Timeout for Lambda functions in seconds"
  type        = number
  default     = 30
}

variable "lambda_memory_size" {
  description = "Memory size for Lambda functions in MB"
  type        = number
  default     = 256
}

# DynamoDB Configuration
variable "dynamodb_billing_mode" {
  description = "Billing mode for DynamoDB tables"
  type        = string
  default     = "PAY_PER_REQUEST"
}

# S3 Configuration
variable "s3_force_destroy" {
  description = "Force destroy S3 buckets even if they contain objects"
  type        = bool
  default     = false
}

# CloudFront Configuration
variable "cloudfront_price_class" {
  description = "Price class for CloudFront distributions"
  type        = string
  default     = "PriceClass_100"
}

# Cognito Configuration
variable "cognito_password_min_length" {
  description = "Minimum password length for Cognito"
  type        = number
  default     = 8
}

variable "cognito_token_validity_hours" {
  description = "Token validity in hours for Cognito"
  type        = number
  default     = 1
}

variable "cognito_refresh_token_validity_days" {
  description = "Refresh token validity in days for Cognito"
  type        = number
  default     = 30
}

# Feature Flags
variable "enable_deletion_protection" {
  description = "Enable deletion protection for critical resources"
  type        = bool
  default     = false
}

variable "enable_point_in_time_recovery" {
  description = "Enable point-in-time recovery for DynamoDB"
  type        = bool
  default     = false
}

variable "enable_auto_verify_email" {
  description = "Enable automatic email verification in Cognito"
  type        = bool
  default     = true
}

# Monitoring and Logging
variable "enable_api_gateway_logging" {
  description = "Enable API Gateway access logging"
  type        = bool
  default     = true
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 14
}

# Tags
variable "additional_tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}
