import {
    CognitoIdentityProviderClient,
    AdminInitiateAuthCommand,
    type AdminInitiateAuthCommandInput,
    type AuthFlowType
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    QueryCommand,
    type QueryCommandInput
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Types
interface SignInRequest {
    email: string;
    password: string;
}

interface UserRecord {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Sign in handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('SignIn Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, password }: SignInRequest = JSON.parse(event.body);

        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }

        // Normalize email to lowercase for case-insensitive handling
        const normalizedEmail = email.toLowerCase();

        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID) {
            return createResponse(500, { error: 'Cognito configuration missing' });
        }

        // Authenticate with Cognito
        const authParams: AdminInitiateAuthCommandInput = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'ADMIN_USER_PASSWORD_AUTH' as AuthFlowType,
            AuthParameters: {
                USERNAME: normalizedEmail,
                PASSWORD: password
            }
        };

        const authCommand = new AdminInitiateAuthCommand(authParams);
        const authResult = await cognitoClient.send(authCommand);

        if (!USERS_TABLE) {
            return createResponse(500, { error: 'Users table configuration missing' });
        }

        // Get user details from DynamoDB
        const queryCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'EmailIndex',
            KeyConditionExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': normalizedEmail
            }
        });
        const userResult = await dynamodb.send(queryCommand);

        if (!userResult.Items || userResult.Items.length === 0) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Items[0] as UserRecord;

        if (!authResult.AuthenticationResult) {
            return createResponse(401, { error: 'Authentication failed' });
        }

        // Check if user has a username set
        if (!user.username) {
            return createResponse(200, {
                message: 'Sign in successful',
                requiresUsername: true,
                tokens: {
                    accessToken: authResult.AuthenticationResult.AccessToken,
                    refreshToken: authResult.AuthenticationResult.RefreshToken,
                    idToken: authResult.AuthenticationResult.IdToken
                },
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName
                }
            });
        }

        return createResponse(200, {
            message: 'Sign in successful',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                refreshToken: authResult.AuthenticationResult.RefreshToken,
                idToken: authResult.AuthenticationResult.IdToken
            },
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName
            }
        });

    } catch (error: any) {
        console.error('SignIn error:', error);

        // Handle specific Cognito errors
        if (error.name === 'NotAuthorizedException') {
            return createResponse(401, {
                error: 'Invalid email or password',
                details: 'The email or password you entered is incorrect.'
            });
        } else if (error.name === 'UserNotConfirmedException') {
            return createResponse(401, {
                error: 'Email not confirmed',
                details: 'Please check your email and confirm your account before signing in.'
            });
        } else if (error.name === 'UserNotFoundException') {
            return createResponse(401, {
                error: 'Invalid email or password',
                details: 'The email or password you entered is incorrect.'
            });
        } else if (error.name === 'TooManyRequestsException') {
            return createResponse(429, {
                error: 'Too many attempts',
                details: 'Too many failed sign-in attempts. Please try again later.'
            });
        } else if (error.name === 'InvalidParameterException') {
            return createResponse(400, {
                error: 'Invalid request',
                details: 'Please check your email and password format.'
            });
        } else {
            return createResponse(500, {
                error: 'Authentication failed',
                details: 'An unexpected error occurred. Please try again.'
            });
        }
    }
};
